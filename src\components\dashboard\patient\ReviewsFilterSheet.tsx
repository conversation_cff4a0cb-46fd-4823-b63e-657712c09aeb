import { useState } from "react";
import { X } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import MultiAsyncSelect from "@/components/common/MultiAsyncSelect";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import type { DateRange } from "@/components/common/Datepicker/DatePicker";

export interface ReviewsFilterData {
	locations: string[];
	providers: string[];
	services: string[];
	categories: string[];
	dateRange: DateRange | undefined;
}

interface ReviewsFilterSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onApplyFilters: (filters: ReviewsFilterData) => void;
	onResetFilters: () => void;
}

const mockLocationOptions = [
	{ value: "1", label: "Location name 1" },
	{ value: "2", label: "Location name 2" },
	{ value: "3", label: "Location name 3" },
	{ value: "4", label: "Downtown Clinic" },
	{ value: "5", label: "Uptown Medical Center" },
];

const mockProviderOptions = [
	{ value: "1", label: "Dr. John Lee" },
	{ value: "2", label: "Dr. Cunter Halls" },
	{ value: "3", label: "Dr. Seth Cohen" },
	{ value: "4", label: "Dr. Sarah Wilson" },
	{ value: "5", label: "Dr. Michael Brown" },
];

const mockServiceOptions = [
	{ value: "1", label: "Blood Work" },
	{ value: "2", label: "Immunization" },
	{ value: "3", label: "Lab Tests" },
	{ value: "4", label: "Consultation" },
	{ value: "5", label: "Physical Exam" },
];

const mockCategoryOptions = [
	{ value: "1", label: "All" },
	{ value: "2", label: "Excellent" },
	{ value: "3", label: "Good" },
	{ value: "4", label: "Average" },
	{ value: "5", label: "Poor" },
];

export function ReviewsFilterSheet({
	open,
	onOpenChange,
	onApplyFilters,
	onResetFilters,
}: ReviewsFilterSheetProps) {
	const [selectedLocations, setSelectedLocations] = useState<string[]>([
		"1",
		"2",
		"3",
	]);
	const [selectedProviders, setSelectedProviders] = useState<string[]>([
		"1",
		"2",
		"3",
	]);
	const [selectedServices, setSelectedServices] = useState<string[]>([
		"1",
		"2",
		"3",
	]);
	const [selectedCategories, setSelectedCategories] = useState<string[]>([
		"1",
	]);
	const [dateRange, setDateRange] = useState<DateRange | undefined>(
		undefined
	);

	const handleApply = () => {
		onApplyFilters({
			locations: selectedLocations,
			providers: selectedProviders,
			services: selectedServices,
			categories: selectedCategories,
			dateRange,
		});
		onOpenChange(false);
	};

	const handleReset = () => {
		setSelectedLocations([]);
		setSelectedProviders([]);
		setSelectedServices([]);
		setSelectedCategories([]);
		setDateRange(undefined);
		onResetFilters();
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full overflow-y-auto px-9 py-9 sm:w-[540px] sm:max-w-[525px] [&>button]:hidden">
				<SheetHeader className="p-0">
					<SheetTitle className="flex items-center justify-between">
						<span>Filter Reviews</span>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="h-6 w-6"
						>
							<X className="h-4 w-4" />
						</Button>
					</SheetTitle>
					<p className="text-muted-foreground text-sm">
						Select options below to help filter your search
					</p>
				</SheetHeader>

				<div className="space-y-4 py-6">
					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Locations
						</Label>
						<MultiAsyncSelect
							options={mockLocationOptions}
							onValueChange={setSelectedLocations}
							defaultValue={selectedLocations}
							placeholder="Select locations"
							className="w-full"
						/>
					</div>

					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Providers
						</Label>
						<MultiAsyncSelect
							options={mockProviderOptions}
							onValueChange={setSelectedProviders}
							defaultValue={selectedProviders}
							placeholder="Select providers"
							className="w-full"
						/>
					</div>

					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Services
						</Label>
						<MultiAsyncSelect
							options={mockServiceOptions}
							onValueChange={setSelectedServices}
							defaultValue={selectedServices}
							placeholder="Select services"
							className="w-full"
						/>
					</div>

					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Categories
						</Label>
						<MultiAsyncSelect
							options={mockCategoryOptions}
							onValueChange={setSelectedCategories}
							defaultValue={selectedCategories}
							placeholder="Select categories"
							className="w-full"
						/>
					</div>

					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Select a Date or Range
						</Label>
						<DatePicker
							variant="range"
							value={dateRange}
							onChange={(range) =>
								setDateRange(range as DateRange)
							}
							placeholder="Pick a date"
							className="w-full"
						/>
					</div>
				</div>

				<SheetFooter className="flex-row justify-between">
					<Button
						variant="ghost"
						onClick={handleReset}
						className="text-muted-foreground hover:text-foreground"
					>
						Reset
					</Button>
					<div className="flex gap-3">
						<Button
							variant="outline"
							onClick={() => onOpenChange(false)}
						>
							Cancel
						</Button>
						<Button onClick={handleApply}>Apply</Button>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
